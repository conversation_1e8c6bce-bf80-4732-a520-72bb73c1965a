#!/usr/bin/env python3
"""
Reconstructed main.py for AugmentMagic application
Based on the actual UI screenshot and analysis
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import sys
import platform
import webbrowser
from datetime import datetime
import threading
import time

class AugmentMagicApp:
    """Main application class for AugmentMagic"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment Magic")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # Set application icon if available
        icon_path = self.get_resource_path("icons/app_icon.ico")
        if os.path.exists(icon_path):
            try:
                self.root.iconbitmap(icon_path)
            except:
                pass

        self.setup_ui()
        self.log_message("程序启动完成...")

    def get_resource_path(self, relative_path):
        """Get absolute path to resource, works for dev and for PyInstaller"""
        try:
            # PyInstaller creates a temp folder and stores path in _MEIPASS
            base_path = sys._MEIPASS
        except Exception:
            base_path = os.path.abspath(".")

        return os.path.join(base_path, relative_path)

    def setup_ui(self):
        """Setup the user interface based on the screenshot"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Program info section
        self.create_program_info_section(main_frame)

        # Steps section
        self.create_steps_section(main_frame)

        # Log section
        self.create_log_section(main_frame)

    def create_program_info_section(self, parent):
        """Create the program information section"""
        info_frame = ttk.LabelFrame(parent, text="程序信息", padding="10")
        info_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Icon and basic info
        info_content_frame = ttk.Frame(info_frame)
        info_content_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        info_content_frame.columnconfigure(1, weight=1)

        # Create a colored label to simulate the icon
        icon_label = tk.Label(info_content_frame, text="AM",
                             bg="#6B46C1", fg="white",
                             font=("Arial", 16, "bold"),
                             width=4, height=2)
        icon_label.grid(row=0, column=0, rowspan=4, padx=(0, 15), pady=5)

        # Program details
        ttk.Label(info_content_frame, text="程序名称:", font=("Arial", 9)).grid(row=0, column=1, sticky=tk.W)
        ttk.Label(info_content_frame, text="Augment Magic", font=("Arial", 9, "bold")).grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

        ttk.Label(info_content_frame, text="程序版本:", font=("Arial", 9)).grid(row=1, column=1, sticky=tk.W)
        ttk.Label(info_content_frame, text="v1.0.3", font=("Arial", 9)).grid(row=1, column=2, sticky=tk.W, padx=(10, 0))

        ttk.Label(info_content_frame, text="系统信息:", font=("Arial", 9)).grid(row=2, column=1, sticky=tk.W)
        system_info = f"{platform.system()} {platform.machine()}"
        ttk.Label(info_content_frame, text=system_info, font=("Arial", 9)).grid(row=2, column=2, sticky=tk.W, padx=(10, 0))

        # Warning message
        warning_frame = ttk.Frame(info_content_frame)
        warning_frame.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(warning_frame, text="官方店铺:", font=("Arial", 9)).grid(row=0, column=0, sticky=tk.W)
        warning_link = tk.Label(warning_frame,
                               text="⚠️ 请认准唯一店铺：小懒老铺口 | ⚠️ 谨防买家生存子没权限！！！黄法欢迎！！！",
                               fg="red", font=("Arial", 9), cursor="hand2")
        warning_link.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        warning_link.bind("<Button-1>", self.open_official_store)

        # Reference document
        ref_frame = ttk.Frame(info_content_frame)
        ref_frame.grid(row=4, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(ref_frame, text="参考文档:", font=("Arial", 9)).grid(row=0, column=0, sticky=tk.W)
        doc_link = tk.Label(ref_frame, text="📋 点击查看详细使用文档",
                           fg="blue", font=("Arial", 9), cursor="hand2")
        doc_link.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        doc_link.bind("<Button-1>", self.open_documentation)

    def create_steps_section(self, parent):
        """Create the three main steps section"""
        # Step 1: Generate Config
        step1_frame = ttk.LabelFrame(parent, text="第一步：生成配置", padding="10")
        step1_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step1_frame.columnconfigure(1, weight=1)

        ttk.Label(step1_frame, text="生成的配置:").grid(row=0, column=0, sticky=tk.W)
        self.config_entry = ttk.Entry(step1_frame, width=60)
        self.config_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))

        button_frame1 = ttk.Frame(step1_frame)
        button_frame1.grid(row=0, column=2, padx=(5, 0))
        ttk.Button(button_frame1, text="生成配置", command=self.generate_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame1, text="复制", command=self.copy_config).pack(side=tk.LEFT)

        # Step 2: Get Verification Code
        step2_frame = ttk.LabelFrame(parent, text="第二步：获取验证码", padding="10")
        step2_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step2_frame.columnconfigure(1, weight=1)

        ttk.Label(step2_frame, text="验证码:").grid(row=0, column=0, sticky=tk.W)
        self.verification_entry = ttk.Entry(step2_frame, width=60)
        self.verification_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))

        button_frame2 = ttk.Frame(step2_frame)
        button_frame2.grid(row=0, column=2, padx=(5, 0))
        ttk.Button(button_frame2, text="获取验证码", command=self.get_verification_code).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame2, text="复制", command=self.copy_verification).pack(side=tk.LEFT)

        # Step 3: Reset Machine Code
        step3_frame = ttk.LabelFrame(parent, text="第三步：重置机器码", padding="10")
        step3_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        step3_frame.columnconfigure(0, weight=1)

        ttk.Label(step3_frame, text="激活码:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.activation_text = tk.Text(step3_frame, height=3, width=80)
        self.activation_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Execute button
        execute_button = ttk.Button(step3_frame, text="执行程序（重置机器码）", command=self.execute_reset)
        execute_button.grid(row=2, column=0, pady=5)

    def create_log_section(self, parent):
        """Create the log section"""
        log_frame = ttk.LabelFrame(parent, text="执行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)

        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Button(log_controls, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))

        self.status_label = ttk.Label(log_controls, text="状态: 就绪")
        self.status_label.pack(side=tk.LEFT)

        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure main frame row weights
        parent.rowconfigure(4, weight=1)

    def log_message(self, message):
        """Add a message to the log with timestamp"""
        timestamp = datetime.now().strftime("[%H:%M:%S]")
        log_entry = f"{timestamp} {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()

    def clear_log(self):
        """Clear the log text area"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def open_official_store(self, event):
        """Open official store link"""
        self.log_message("🔗 打开官方店铺链接...")
        # In a real implementation, this would open the actual store URL
        messagebox.showinfo("官方店铺", "这里会打开官方店铺链接")

    def open_documentation(self, event):
        """Open documentation link"""
        self.log_message("📖 打开使用文档...")
        # In a real implementation, this would open the documentation URL
        messagebox.showinfo("使用文档", "这里会打开详细使用文档")

    def generate_config(self):
        """Generate configuration"""
        self.log_message("🔧 开始生成配置...")
        self.status_label.config(text="状态: 生成配置中...")

        def worker():
            try:
                # Simulate config generation
                time.sleep(1)
                import uuid
                config_value = f"CONFIG_{uuid.uuid4().hex[:8].upper()}"

                # Update UI in main thread
                self.root.after(0, lambda: self.config_entry.delete(0, tk.END))
                self.root.after(0, lambda: self.config_entry.insert(0, config_value))
                self.root.after(0, lambda: self.log_message(f"✅ 配置生成成功: {config_value}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 配置生成完成"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 配置生成失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 配置生成失败"))

        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

    def copy_config(self):
        """Copy configuration to clipboard"""
        config_value = self.config_entry.get()
        if config_value:
            self.root.clipboard_clear()
            self.root.clipboard_append(config_value)
            self.log_message("📋 配置已复制到剪贴板")
        else:
            self.log_message("⚠️ 没有配置可复制")

    def get_verification_code(self):
        """Get verification code"""
        self.log_message("🔐 开始获取验证码...")
        self.status_label.config(text="状态: 获取验证码中...")

        def worker():
            try:
                # Simulate verification code generation
                time.sleep(2)
                import random
                verification_code = f"{random.randint(100000, 999999)}"

                # Update UI in main thread
                self.root.after(0, lambda: self.verification_entry.delete(0, tk.END))
                self.root.after(0, lambda: self.verification_entry.insert(0, verification_code))
                self.root.after(0, lambda: self.log_message(f"✅ 验证码获取成功: {verification_code}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 验证码获取完成"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 验证码获取失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 验证码获取失败"))

        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

    def copy_verification(self):
        """Copy verification code to clipboard"""
        verification_code = self.verification_entry.get()
        if verification_code:
            self.root.clipboard_clear()
            self.root.clipboard_append(verification_code)
            self.log_message("📋 验证码已复制到剪贴板")
        else:
            self.log_message("⚠️ 没有验证码可复制")

    def execute_reset(self):
        """Execute machine code reset"""
        activation_code = self.activation_text.get(1.0, tk.END).strip()
        if not activation_code:
            self.log_message("⚠️ 请输入激活码")
            messagebox.showwarning("警告", "请先输入激活码")
            return

        self.log_message("🚀 开始执行重置机器码...")
        self.status_label.config(text="状态: 执行中...")

        def worker():
            try:
                # Simulate reset process
                self.root.after(0, lambda: self.log_message("📋 请以管理员身份运行程序"))
                time.sleep(1)
                self.root.after(0, lambda: self.log_message("🔍 检测到系统: Windows x86_64"))
                time.sleep(1)
                self.root.after(0, lambda: self.log_message("✅ 初始化成功"))
                time.sleep(1)
                self.root.after(0, lambda: self.log_message("🔄 正在检查软件更新..."))
                time.sleep(2)
                self.root.after(0, lambda: self.log_message("✅ 机器码重置完成"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 执行完成"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 执行失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="状态: 执行失败"))

        thread = threading.Thread(target=worker)
        thread.daemon = True
        thread.start()

    def run(self):
        """Start the application"""
        # Start the main event loop
        self.root.mainloop()

def main():
    """Main entry point"""
    try:
        app = AugmentMagicApp()
        app.run()
    except Exception as e:
        print(f"Application error: {e}")
        messagebox.showerror("Fatal Error", f"Application failed to start: {e}")

if __name__ == "__main__":
    main()
