@echo off
title Augment Magic - 启动器
echo ========================================
echo    Augment Magic - 重构版本启动器
echo ========================================
echo.

REM Check if Python is available
echo [1/3] 检查Python环境...
d:\soft\miniconda\python.exe --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境 d:\soft\miniconda\python.exe
    echo 请更新此脚本中的Python路径，或安装Python到指定位置。
    echo.
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

REM Check if required modules are available
echo [2/3] 检查依赖模块...
d:\soft\miniconda\python.exe -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: tkinter模块未找到
    echo 请确保Python安装包含tkinter支持
    pause
    exit /b 1
)
echo ✅ 依赖模块检查通过

echo [3/3] 启动应用程序...
echo.
echo 🚀 正在启动 Augment Magic...
echo 📝 注意: 这是基于原始exe文件重构的版本
echo 🔧 界面已根据原始程序截图重新设计
echo.

REM Start the application
d:\soft\miniconda\python.exe main_reconstructed.py

echo.
echo 📱 应用程序已关闭
echo 感谢使用 Augment Magic 重构版本！
pause
