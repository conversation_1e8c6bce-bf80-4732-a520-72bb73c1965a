@echo off
echo Starting AugmentMagic Reconstructed Application...
echo.

REM Check if Python is available
d:\soft\miniconda\python.exe --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found at d:\soft\miniconda\python.exe
    echo Please update the path in this script to point to your Python installation.
    pause
    exit /b 1
)

REM Check if required modules are available
echo Checking dependencies...
d:\soft\miniconda\python.exe -c "import tkinter, requests, packaging" >nul 2>&1
if errorlevel 1 (
    echo Error: Required dependencies not found.
    echo Please install dependencies using: pip install -r requirements.txt
    pause
    exit /b 1
)

echo Dependencies OK. Starting application...
echo.

REM Start the application
d:\soft\miniconda\python.exe main_reconstructed.py

echo.
echo Application closed.
pause
