#!/usr/bin/env python3
"""
Reconstructed version_checker.py module
Version checking and update functionality for AugmentMagic application
"""

import requests
import json
import os
import subprocess
import sys
from packaging import version
from pathlib import Path

class VersionChecker:
    """Version checker and updater"""
    
    def __init__(self, current_version="1.0.0"):
        self.current_version = current_version
        self.update_url = "https://api.augmentcode.com/updates"
        self.download_url = "https://releases.augmentcode.com"
    
    def get_current_version(self):
        """Get current application version"""
        return self.current_version
    
    def check_for_updates(self, timeout=10):
        """Check for available updates"""
        try:
            response = requests.get(
                f"{self.update_url}/latest",
                timeout=timeout,
                headers={'User-Agent': f'AugmentMagic/{self.current_version}'}
            )
            
            if response.status_code == 200:
                update_info = response.json()
                latest_version = update_info.get('version')
                
                if latest_version and version.parse(latest_version) > version.parse(self.current_version):
                    return {
                        'update_available': True,
                        'latest_version': latest_version,
                        'current_version': self.current_version,
                        'download_url': update_info.get('download_url'),
                        'release_notes': update_info.get('release_notes', ''),
                        'size': update_info.get('size', 0),
                        'checksum': update_info.get('checksum', '')
                    }
                else:
                    return {
                        'update_available': False,
                        'latest_version': latest_version,
                        'current_version': self.current_version
                    }
            else:
                return {'error': f'HTTP {response.status_code}: {response.text}'}
                
        except requests.RequestException as e:
            return {'error': f'Network error: {str(e)}'}
        except Exception as e:
            return {'error': f'Unexpected error: {str(e)}'}
    
    def download_update(self, download_url, save_path, progress_callback=None):
        """Download update file"""
        try:
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if progress_callback and total_size > 0:
                            progress = (downloaded / total_size) * 100
                            progress_callback(progress)
            
            return True
            
        except Exception as e:
            print(f"Download error: {e}")
            return False
    
    def verify_checksum(self, file_path, expected_checksum):
        """Verify file checksum"""
        import hashlib
        
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            calculated_checksum = sha256_hash.hexdigest()
            return calculated_checksum.lower() == expected_checksum.lower()
            
        except Exception as e:
            print(f"Checksum verification error: {e}")
            return False
    
    def install_update(self, update_file_path):
        """Install update"""
        try:
            if sys.platform.startswith('win'):
                # Windows installer
                subprocess.Popen([update_file_path, '/S'], shell=True)
            elif sys.platform.startswith('darwin'):
                # macOS installer
                subprocess.Popen(['open', update_file_path])
            else:
                # Linux installer
                os.chmod(update_file_path, 0o755)
                subprocess.Popen([update_file_path])
            
            return True
            
        except Exception as e:
            print(f"Installation error: {e}")
            return False
    
    def get_platform_download_url(self, base_url):
        """Get platform-specific download URL"""
        platform_map = {
            'win32': 'windows-x86_64.exe',
            'darwin': 'macos-x86_64.dmg',
            'linux': 'linux-x86_64.AppImage'
        }
        
        if sys.platform.startswith('darwin') and 'arm' in os.uname().machine.lower():
            platform_file = 'macos-aarch64.dmg'
        elif sys.platform.startswith('linux') and 'aarch64' in os.uname().machine:
            platform_file = 'linux-aarch64.AppImage'
        else:
            platform_file = platform_map.get(sys.platform, 'linux-x86_64.AppImage')
        
        return f"{base_url}/augment-magic-{platform_file}"
    
    def auto_update_check(self):
        """Perform automatic update check"""
        try:
            update_info = self.check_for_updates()
            
            if update_info.get('update_available'):
                return {
                    'status': 'update_available',
                    'message': f"Update available: v{update_info['latest_version']}",
                    'data': update_info
                }
            elif 'error' in update_info:
                return {
                    'status': 'error',
                    'message': update_info['error']
                }
            else:
                return {
                    'status': 'up_to_date',
                    'message': 'Application is up to date'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Update check failed: {str(e)}'
            }

# Global version checker instance
version_checker = VersionChecker()
