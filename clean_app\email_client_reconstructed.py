#!/usr/bin/env python3
"""
Reconstructed email_client.py module
Email functionality for AugmentMagic application
"""

import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import os
import ssl
from datetime import datetime

class EmailClient:
    """Email client for sending and receiving emails"""
    
    def __init__(self):
        self.smtp_server = None
        self.imap_server = None
        self.email_config = {
            'smtp_host': 'smtp.gmail.com',
            'smtp_port': 587,
            'imap_host': 'imap.gmail.com',
            'imap_port': 993,
            'use_tls': True
        }
    
    def configure_email(self, smtp_host, smtp_port, imap_host, imap_port, use_tls=True):
        """Configure email server settings"""
        self.email_config.update({
            'smtp_host': smtp_host,
            'smtp_port': smtp_port,
            'imap_host': imap_host,
            'imap_port': imap_port,
            'use_tls': use_tls
        })
    
    def connect_smtp(self, username, password):
        """Connect to SMTP server"""
        try:
            self.smtp_server = smtplib.SMTP(
                self.email_config['smtp_host'], 
                self.email_config['smtp_port']
            )
            
            if self.email_config['use_tls']:
                self.smtp_server.starttls()
            
            self.smtp_server.login(username, password)
            return True
            
        except Exception as e:
            print(f"SMTP connection error: {e}")
            return False
    
    def connect_imap(self, username, password):
        """Connect to IMAP server"""
        try:
            if self.email_config['use_tls']:
                self.imap_server = imaplib.IMAP4_SSL(
                    self.email_config['imap_host'],
                    self.email_config['imap_port']
                )
            else:
                self.imap_server = imaplib.IMAP4(
                    self.email_config['imap_host'],
                    self.email_config['imap_port']
                )
            
            self.imap_server.login(username, password)
            return True
            
        except Exception as e:
            print(f"IMAP connection error: {e}")
            return False
    
    def send_email(self, to_email, subject, body, from_email=None, attachments=None):
        """Send email"""
        try:
            if not self.smtp_server:
                raise Exception("SMTP server not connected")
            
            msg = MIMEMultipart()
            msg['Subject'] = subject
            msg['To'] = to_email
            if from_email:
                msg['From'] = from_email
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Add attachments
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        msg.attach(part)
            
            # Send email
            text = msg.as_string()
            self.smtp_server.sendmail(from_email or to_email, to_email, text)
            
            return True
            
        except Exception as e:
            print(f"Send email error: {e}")
            return False
    
    def get_emails(self, folder='INBOX', limit=10):
        """Get emails from specified folder"""
        try:
            if not self.imap_server:
                raise Exception("IMAP server not connected")
            
            self.imap_server.select(folder)
            
            # Search for all emails
            status, messages = self.imap_server.search(None, 'ALL')
            
            if status != 'OK':
                return []
            
            email_ids = messages[0].split()
            emails = []
            
            # Get the latest emails (limited by limit parameter)
            for email_id in email_ids[-limit:]:
                status, msg_data = self.imap_server.fetch(email_id, '(RFC822)')
                
                if status == 'OK':
                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)
                    
                    email_info = {
                        'id': email_id.decode(),
                        'subject': email_message['Subject'],
                        'from': email_message['From'],
                        'to': email_message['To'],
                        'date': email_message['Date'],
                        'body': self._get_email_body(email_message)
                    }
                    
                    emails.append(email_info)
            
            return emails
            
        except Exception as e:
            print(f"Get emails error: {e}")
            return []
    
    def _get_email_body(self, email_message):
        """Extract email body from email message"""
        body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    body = part.get_payload(decode=True).decode()
                    break
        else:
            body = email_message.get_payload(decode=True).decode()
        
        return body
    
    def close_connections(self):
        """Close email server connections"""
        try:
            if self.smtp_server:
                self.smtp_server.quit()
                self.smtp_server = None
        except:
            pass
        
        try:
            if self.imap_server:
                self.imap_server.close()
                self.imap_server.logout()
                self.imap_server = None
        except:
            pass
    
    def send_notification_email(self, recipient, title, message):
        """Send notification email"""
        subject = f"AugmentMagic Notification: {title}"
        body = f"""
AugmentMagic Notification

Title: {title}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Message:
{message}

---
This is an automated message from AugmentMagic.
"""
        
        return self.send_email(recipient, subject, body)

# Global email client instance
email_client = EmailClient()
