#!/usr/bin/env python3
"""
Helper script to attempt decompilation of pyc files
"""
import os
import sys
import marshal
import dis
import struct
from pathlib import Path

def try_decompile_pyc(pyc_path, output_dir):
    """尝试反编译pyc文件"""
    try:
        with open(pyc_path, 'rb') as f:
            # 读取pyc文件头
            magic = f.read(4)
            print(f"Magic bytes: {magic.hex()}")
            
            # 跳过时间戳和大小信息
            f.read(12)  # Python 3.7+的格式
            
            # 尝试加载代码对象
            try:
                code = marshal.load(f)
                print(f"Successfully loaded code object from {pyc_path}")
                
                # 创建输出文件
                output_path = Path(output_dir) / (Path(pyc_path).stem + "_disasm.txt")
                with open(output_path, 'w', encoding='utf-8') as out:
                    out.write(f"# Disassembly of {pyc_path}\n")
                    out.write(f"# Magic: {magic.hex()}\n\n")
                    
                    # 反汇编代码
                    dis.dis(code, file=out)
                    
                    # 尝试提取字符串常量
                    out.write("\n\n# Constants:\n")
                    if hasattr(code, 'co_consts'):
                        for i, const in enumerate(code.co_consts):
                            out.write(f"# {i}: {repr(const)}\n")
                    
                    # 提取变量名
                    out.write("\n# Variable names:\n")
                    if hasattr(code, 'co_names'):
                        for i, name in enumerate(code.co_names):
                            out.write(f"# {i}: {name}\n")
                
                print(f"Disassembly saved to {output_path}")
                return True
                
            except Exception as e:
                print(f"Failed to load marshal data from {pyc_path}: {e}")
                return False
                
    except Exception as e:
        print(f"Error processing {pyc_path}: {e}")
        return False

def main():
    # 创建输出目录
    output_dir = "decompiled_source/disassembly"
    os.makedirs(output_dir, exist_ok=True)
    
    # 要处理的关键文件
    key_files = [
        "decompiled_source/main.pyc",
        "decompiled_source/PYZ.pyz_extract/auth.pyc",
        "decompiled_source/PYZ.pyz_extract/config.pyc", 
        "decompiled_source/PYZ.pyz_extract/email_client.pyc",
        "decompiled_source/PYZ.pyz_extract/version_checker.pyc"
    ]
    
    success_count = 0
    for pyc_file in key_files:
        if os.path.exists(pyc_file):
            print(f"\nProcessing {pyc_file}...")
            if try_decompile_pyc(pyc_file, output_dir):
                success_count += 1
        else:
            print(f"File not found: {pyc_file}")
    
    print(f"\nSuccessfully processed {success_count} files")

if __name__ == "__main__":
    main()
