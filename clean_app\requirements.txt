# AugmentMagic Dependencies
# Based on analysis of the extracted files

# Core dependencies
requests>=2.25.0
urllib3>=1.26.0
certifi>=2021.5.25
charset-normalizer>=2.0.0
idna>=3.0

# Packaging and version management
packaging>=21.0

# Email functionality (built-in modules, no additional requirements)
# - smtplib (built-in)
# - imaplib (built-in)
# - email (built-in)

# GUI framework (built-in)
# - tkinter (built-in)

# Other built-in modules used
# - json (built-in)
# - os (built-in)
# - sys (built-in)
# - threading (built-in)
# - subprocess (built-in)
# - pathlib (built-in)
# - datetime (built-in)
# - hashlib (built-in)
# - base64 (built-in)
# - ssl (built-in)

# Development dependencies (optional)
# pyinstaller>=4.0  # For rebuilding the executable
# pytest>=6.0       # For testing
# black>=21.0        # For code formatting
# flake8>=3.8        # For linting
