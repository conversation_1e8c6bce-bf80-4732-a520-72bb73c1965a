# AugmentMagic - 重构应用程序

这是从 `AugmentMagic.exe` 反编译并重构的Python应用程序的干净版本。

## 🚀 快速启动

### 方法1：使用批处理文件（推荐）
双击 `start_app.bat` 文件即可启动应用程序。

### 方法2：命令行启动
```bash
# 确保使用正确的Python环境
d:\soft\miniconda\python.exe main_reconstructed.py
```

### 方法3：如果你有其他Python环境
```bash
# 首先安装依赖
pip install -r requirements.txt

# 然后启动应用程序
python main_reconstructed.py
```

## 📁 文件结构

```
clean_app/
├── main_reconstructed.py           # 主应用程序
├── config_reconstructed.py         # 配置管理模块
├── auth_reconstructed.py           # 认证模块
├── email_client_reconstructed.py   # 邮件客户端模块
├── version_checker_reconstructed.py # 版本检查模块
├── test_modules.py                 # 模块测试脚本
├── start_app.bat                   # Windows启动脚本
├── requirements.txt                # Python依赖
├── README.md                       # 本文件
├── icons/                          # 应用程序图标
│   ├── app_icon.ico
│   └── icon_*.png
└── data/                           # 数据文件
    ├── __init__.py
    └── augment-magic-*             # 各平台可执行文件
```

## 🧪 测试功能

运行测试脚本来验证所有模块是否正常工作：

```bash
d:\soft\miniconda\python.exe test_modules.py
```

## ✨ 应用程序功能

### 主界面
- **文本输入区域**: 可以输入要处理的文本
- **处理按钮**: 处理输入的文本（当前为演示功能）
- **清除按钮**: 清空输入区域
- **设置按钮**: 打开设置对话框
- **关于按钮**: 显示应用程序信息

### 模块功能

#### 配置管理 (config_reconstructed.py)
- 支持JSON配置文件
- 点号分隔的配置键访问
- 默认配置值
- 自动保存配置

#### 认证系统 (auth_reconstructed.py)
- 密码哈希和验证
- 令牌生成和管理
- 用户会话管理
- 自动令牌过期处理

#### 邮件客户端 (email_client_reconstructed.py)
- SMTP邮件发送
- IMAP邮件接收
- 附件支持
- 多种邮件服务器支持

#### 版本检查 (version_checker_reconstructed.py)
- 自动更新检查
- 跨平台更新支持
- 文件校验和验证
- 下载进度跟踪

## 🔧 配置

应用程序会在首次运行时创建 `config.json` 文件。你可以编辑此文件来自定义设置：

```json
{
    "app": {
        "name": "AugmentMagic",
        "version": "1.0.0",
        "debug": false
    },
    "ui": {
        "theme": "default",
        "window_size": "800x600",
        "auto_save": true
    },
    "network": {
        "api_endpoint": "https://api.augmentcode.com",
        "timeout": 30,
        "retry_count": 3
    }
}
```

## 🐛 故障排除

### 应用程序无法启动
1. 确保Python已正确安装
2. 检查依赖是否已安装：`pip install -r requirements.txt`
3. 尝试在命令行中运行以查看错误信息

### 模块导入错误
确保你在正确的目录中运行应用程序，并且所有 `*_reconstructed.py` 文件都在同一目录中。

### GUI不显示
确保你的系统支持tkinter。在某些Linux发行版中，你可能需要安装 `python3-tk` 包。

## 📝 注意事项

1. **重构代码**: 这些是基于原始exe文件分析重构的代码，可能与原始实现有差异
2. **网络功能**: 版本检查和某些网络功能使用的是示例API端点，需要根据实际情况调整
3. **功能完整性**: 某些高级功能可能需要进一步开发和完善

## 🔄 更新历史

- **v1.0.0**: 初始重构版本，包含基本GUI和核心模块功能

## 📞 支持

如果遇到问题或需要帮助，请检查：
1. 所有依赖是否正确安装
2. Python版本是否兼容（推荐Python 3.8+）
3. 配置文件是否正确

---

**免责声明**: 此应用程序是基于反编译分析重构的，仅用于学习和研究目的。
