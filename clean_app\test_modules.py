#!/usr/bin/env python3
"""
测试重构模块的功能
"""

def test_config_module():
    """测试配置模块"""
    print("=== 测试配置模块 ===")
    try:
        from config_reconstructed import config
        print(f"应用名称: {config.get_app_name()}")
        print(f"应用版本: {config.get_app_version()}")
        print(f"API端点: {config.get_api_endpoint()}")
        print(f"窗口大小: {config.get_window_size()}")
        print(f"调试模式: {config.is_debug_mode()}")
        print("✅ 配置模块测试通过")
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
    print()

def test_auth_module():
    """测试认证模块"""
    print("=== 测试认证模块 ===")
    try:
        from auth_reconstructed import auth
        
        # 测试密码哈希
        password = "test123"
        hashed = auth.hash_password(password)
        print(f"密码哈希长度: {len(hashed)}")
        
        # 测试密码验证
        is_valid = auth.verify_password(hashed, password)
        print(f"密码验证: {is_valid}")
        
        # 测试认证状态
        print(f"当前认证状态: {auth.is_authenticated()}")
        
        # 测试用户认证
        auth_result = auth.authenticate_user("testuser", "testpass")
        print(f"用户认证结果: {auth_result}")
        print(f"认证后状态: {auth.is_authenticated()}")
        
        print("✅ 认证模块测试通过")
    except Exception as e:
        print(f"❌ 认证模块测试失败: {e}")
    print()

def test_version_checker_module():
    """测试版本检查模块"""
    print("=== 测试版本检查模块 ===")
    try:
        from version_checker_reconstructed import version_checker
        
        print(f"当前版本: {version_checker.get_current_version()}")
        
        # 测试自动更新检查（不实际连接网络）
        print("执行自动更新检查...")
        result = version_checker.auto_update_check()
        print(f"更新检查结果: {result['status']}")
        print(f"消息: {result['message']}")
        
        print("✅ 版本检查模块测试通过")
    except Exception as e:
        print(f"❌ 版本检查模块测试失败: {e}")
    print()

def test_email_client_module():
    """测试邮件客户端模块"""
    print("=== 测试邮件客户端模块 ===")
    try:
        from email_client_reconstructed import email_client
        
        # 测试配置
        email_client.configure_email(
            smtp_host="smtp.example.com",
            smtp_port=587,
            imap_host="imap.example.com", 
            imap_port=993
        )
        print("邮件客户端配置完成")
        
        print("✅ 邮件客户端模块测试通过")
    except Exception as e:
        print(f"❌ 邮件客户端模块测试失败: {e}")
    print()

def main():
    """主测试函数"""
    print("开始测试重构的模块...")
    print("=" * 50)
    
    test_config_module()
    test_auth_module()
    test_version_checker_module()
    test_email_client_module()
    
    print("=" * 50)
    print("所有模块测试完成！")

if __name__ == "__main__":
    main()
