#!/usr/bin/env python3
"""
Reconstructed config.py module
Configuration management for AugmentMagic application
"""

import json
import os
from pathlib import Path

class ConfigManager:
    """Configuration manager for the application"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def get_default_config(self):
        """Return default configuration"""
        return {
            "app": {
                "name": "AugmentMagic",
                "version": "1.0.0",
                "debug": False
            },
            "ui": {
                "theme": "default",
                "window_size": "800x600",
                "auto_save": True
            },
            "network": {
                "api_endpoint": "https://api.augmentcode.com",
                "timeout": 30,
                "retry_count": 3
            },
            "paths": {
                "data_dir": "data",
                "icons_dir": "icons",
                "temp_dir": "temp"
            }
        }
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                self.config_data = self.get_default_config()
                self.save_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            self.config_data = self.get_default_config()
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key, default=None):
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key, value):
        """Set configuration value by key (supports dot notation)"""
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_app_name(self):
        """Get application name"""
        return self.get('app.name', 'AugmentMagic')
    
    def get_app_version(self):
        """Get application version"""
        return self.get('app.version', '1.0.0')
    
    def get_api_endpoint(self):
        """Get API endpoint"""
        return self.get('network.api_endpoint', 'https://api.augmentcode.com')
    
    def get_window_size(self):
        """Get window size"""
        return self.get('ui.window_size', '800x600')
    
    def is_debug_mode(self):
        """Check if debug mode is enabled"""
        return self.get('app.debug', False)

# Global config instance
config = ConfigManager()
