#!/usr/bin/env python3
"""
Reconstructed auth.py module
Authentication and authorization for AugmentMagic application
"""

import hashlib
import base64
import json
import os
from datetime import datetime, timedelta

class AuthManager:
    """Authentication manager"""
    
    def __init__(self):
        self.token = None
        self.user_info = None
        self.token_file = "auth_token.json"
    
    def hash_password(self, password, salt=None):
        """Hash password with salt"""
        if salt is None:
            salt = os.urandom(32)
        
        pwdhash = hashlib.pbkdf2_hmac('sha256', 
                                     password.encode('utf-8'), 
                                     salt, 
                                     100000)
        return salt + pwdhash
    
    def verify_password(self, stored_password, provided_password):
        """Verify password against stored hash"""
        salt = stored_password[:32]
        stored_hash = stored_password[32:]
        
        pwdhash = hashlib.pbkdf2_hmac('sha256',
                                     provided_password.encode('utf-8'),
                                     salt,
                                     100000)
        return pwdhash == stored_hash
    
    def generate_token(self, user_id, expires_in_hours=24):
        """Generate authentication token"""
        import secrets
        
        token_data = {
            'user_id': user_id,
            'issued_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=expires_in_hours)).isoformat(),
            'token': secrets.token_urlsafe(32)
        }
        
        return token_data
    
    def save_token(self, token_data):
        """Save token to file"""
        try:
            with open(self.token_file, 'w') as f:
                json.dump(token_data, f)
            self.token = token_data
        except Exception as e:
            print(f"Error saving token: {e}")
    
    def load_token(self):
        """Load token from file"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r') as f:
                    token_data = json.load(f)
                
                # Check if token is still valid
                expires_at = datetime.fromisoformat(token_data['expires_at'])
                if datetime.now() < expires_at:
                    self.token = token_data
                    return True
                else:
                    # Token expired, remove it
                    os.remove(self.token_file)
                    return False
        except Exception as e:
            print(f"Error loading token: {e}")
        
        return False
    
    def is_authenticated(self):
        """Check if user is authenticated"""
        return self.token is not None
    
    def get_user_id(self):
        """Get current user ID"""
        if self.token:
            return self.token.get('user_id')
        return None
    
    def logout(self):
        """Logout user"""
        self.token = None
        self.user_info = None
        
        if os.path.exists(self.token_file):
            try:
                os.remove(self.token_file)
            except Exception as e:
                print(f"Error removing token file: {e}")
    
    def authenticate_user(self, username, password):
        """Authenticate user with username and password"""
        # This would typically validate against a database or API
        # For now, we'll simulate authentication
        
        if username and password:
            # Simulate successful authentication
            token_data = self.generate_token(username)
            self.save_token(token_data)
            
            self.user_info = {
                'username': username,
                'authenticated_at': datetime.now().isoformat()
            }
            
            return True
        
        return False
    
    def get_auth_header(self):
        """Get authorization header for API requests"""
        if self.token:
            return {'Authorization': f"Bearer {self.token['token']}"}
        return {}

# Global auth instance
auth = AuthManager()
