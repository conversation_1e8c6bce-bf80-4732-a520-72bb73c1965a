2025-08-01 21:28:39,736 [+] pydumpck is a multi-threads tool for decompile exe,elf,pyz,pyc packed by python which is base on pycdc and uncompyle6.sometimes its py-file result not exactly right ,maybe could use uncompyle6.
--------------------
pydumpck initilizing with 1.20.1 :pyinstaller_dump.py:120-20  D:\soft\miniconda\Lib\site-packages\pydumpck\pyinstaller_dump.py on(pyinstaller_dump.run) at 14824@MainThread  
2025-08-01 21:28:39,737 [*] plugins loaded with ['pycdc'] :__init__.py:165-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.load_plugins) at 14824@MainThread  
2025-08-01 21:28:39,737 [*] target file input:AugmentMagic.exe
to:decompiled_source :__init__.py:206-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.main) at 14824@MainThread  
2025-08-01 21:28:39,739 [*] start dump target file. type:arch :__init__.py:213-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.main) at 14824@MainThread  
2025-08-01 21:28:42,746 [+] struct file found:b'' :__init__.py:136-20  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.add) at 14824@MainThread  
2025-08-01 21:28:42,746 [*] 
export pyc :__init__.py:94-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 14824@MainThread  
2025-08-01 21:28:45,851 [*] 
decompile pyc :__init__.py:96-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 14824@MainThread  
2025-08-01 21:28:45,863 [!] Exception on decompile bytecode file:decompiled_source\pyimod02_importers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:45,865 [!] Exception on decompile bytecode file:decompiled_source\pyimod04_pywin32.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:45,867 [!] Exception on decompile bytecode file:decompiled_source\pyimod03_ctypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:45,870 [!] Exception on decompile bytecode file:decompiled_source\pyi_rth__tkinter.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:45,872 [!] Exception on decompile bytecode file:decompiled_source\pyi_rth_multiprocessing.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:45,873 [!] Exception on decompile bytecode file:decompiled_source\pyi_rth_inspect.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:45,874 [!] Exception on decompile bytecode file:decompiled_source\pyi_rth_setuptools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:45,875 [!] Exception on decompile bytecode file:decompiled_source\struct.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:45,876 [!] Exception on decompile bytecode file:decompiled_source\pyimod01_archive.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:45,877 [!] Exception on decompile bytecode file:decompiled_source\pyi_rth_pkgutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:45,879 [!] Exception on decompile bytecode file:decompiled_source\pyiboot01_bootstrap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:45,881 [!] Exception on decompile bytecode file:decompiled_source\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:46,857 [*] 
extract pyz :__init__.py:98-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 14824@MainThread  
2025-08-01 21:28:46,859 [-] Warning: This script is running in a different Python version than the one used to build the executable. :pyz_extract.py:48-40  D:\soft\miniconda\Lib\site-packages\pydumpck\pyc_checker\pyz_extract.py on(pyz_extract._handle_file) at 14824@MainThread  
2025-08-01 21:28:46,863 [+] Found 539 files in PYZ archive :pyz_extract.py:60-20  D:\soft\miniconda\Lib\site-packages\pydumpck\pyc_checker\pyz_extract.py on(pyz_extract._handle_file) at 14824@MainThread  
2025-08-01 21:28:48,083 [*] 
decompile pyc for `extract pyz` :__init__.py:100-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 14824@MainThread  
2025-08-01 21:28:48,130 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_compression.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:48,131 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_aix_support.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:48,133 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\selector_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:48,135 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pydatetime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:48,142 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\mixins.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:48,145 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\appdirs.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:48,148 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:48,149 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\base_futures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:48,150 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\streams.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:48,151 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\_minimal_curses.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:48,152 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\__future__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:48,153 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\subprocess.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:48,155 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:48,156 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\taskgroups.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:48,157 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\futures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:48,158 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\protocols.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:48,160 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_distutils_hack\override.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:48,162 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\locks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:48,163 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\windows_console.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:48,164 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:48,166 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pydecimal.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:48,167 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\_threading_handler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:48,168 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:48,169 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_strptime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:48,170 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\argparse.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:48,171 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\runners.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:48,172 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_threading_local.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:48,174 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\fancy_termios.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:48,175 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\base_subprocess.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:48,178 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\commands.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:48,179 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\proactor_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:48,180 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\input.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:48,182 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\tasks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:48,184 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\keymap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:48,185 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\historical_reader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:48,188 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\simple_interact.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:48,189 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\base_tasks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:48,190 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\queues.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:48,191 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_sitebuiltins.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:48,192 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\base_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:48,194 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\curses.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:48,195 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\coroutines.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:48,196 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\trace.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:48,198 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ast.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:48,198 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:48,199 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_py_abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:48,202 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\readline.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:48,202 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\sslproto.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:48,204 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_ios_support.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:48,205 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\console.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:48,206 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\staggered.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:48,206 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_colorize.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:48,207 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\format_helpers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:48,208 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_distutils_hack\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:48,208 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\constants.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:48,209 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:48,211 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\completing_reader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:48,213 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\reader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:48,213 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\threads.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:48,218 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_compat_pickle.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:48,219 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\unix_eventqueue.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:48,220 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\pager.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:48,220 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\transports.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:48,221 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\types.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:48,222 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:48,223 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_pyrepl\unix_console.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:48,230 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\_opcode_metadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:48,231 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\unix_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:48,233 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\windows_events.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:48,234 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\timeouts.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:48,237 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\windows_utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:48,238 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\asyncio\trsock.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:48,241 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\auth.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:48,242 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\bisect.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:48,244 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\base64.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:48,247 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\bz2.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:48,248 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\backports\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:48,250 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\certifi\core.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:48,252 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\calendar.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:48,253 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:48,254 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\certifi\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:48,258 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\api.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:48,273 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\constant.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:48,276 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\cd.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:48,278 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:48,279 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\models.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:48,281 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:48,282 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\codeop.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:48,283 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\code.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:48,285 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\concurrent\futures\process.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:48,286 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\concurrent\futures\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:48,287 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\concurrent\futures\thread.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:48,289 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\config.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:48,289 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\concurrent\futures\_base.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:48,290 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\charset_normalizer\legacy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:48,293 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\configparser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:48,294 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\concurrent\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:48,297 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\contextlib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:48,298 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\copy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:48,301 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\csv.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:48,303 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:48,304 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\macholib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:48,306 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\contextvars.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:48,307 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\macholib\dyld.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:48,308 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\_aix.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:48,309 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\macholib\dylib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:48,312 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\curses\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:48,316 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\macholib\framework.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:48,317 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\_endian.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:48,319 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\wintypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:48,320 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\decimal.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:48,322 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ctypes\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:48,323 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\dis.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:48,324 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:48,326 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\difflib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:48,327 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\datetime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:48,332 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\curses\has_key.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:48,332 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\dataclasses.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:48,334 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\_encoded_words.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:48,334 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\_header_value_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:48,336 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\base64mime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:48,339 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\_parseaddr.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:48,343 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\_policybase.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:48,345 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:48,347 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\feedparser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:48,349 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\encoders.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:48,351 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\iterators.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:48,352 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\generator.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:48,354 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\headerregistry.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:48,355 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\policy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:48,357 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\header.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:48,358 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\message.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:48,361 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\contentmanager.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:48,363 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:48,365 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email_client.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:48,367 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\charset.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:48,367 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\quoprimime.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:48,369 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\fnmatch.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:48,370 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ftplib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:48,371 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\getopt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:48,371 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\email\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:48,373 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\fractions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:48,376 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\getpass.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:48,377 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:48,379 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\gettext.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:48,380 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\hashlib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:48,382 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\gzip.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:48,383 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\hmac.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:48,385 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\html\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:48,386 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\http\client.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:48,601 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\html\entities.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:48,604 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\http\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:48,606 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\http\cookiejar.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:48,608 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\http\server.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:48,610 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\http\cookies.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:48,613 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\idna\package_data.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:48,614 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\idna\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:48,758 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\idna\idnadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:48,761 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:48,764 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\idna\intranges.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:48,765 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\idna\core.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:48,769 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\idna\uts46data.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:48,771 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\_bootstrap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:48,772 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\_abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:48,773 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\_bootstrap_external.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:48,775 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:48,778 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\machinery.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:48,782 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:48,784 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:48,788 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:48,788 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\readers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:48,789 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:48,790 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\_functools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:48,790 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\_collections.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:48,794 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\_text.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:48,797 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\metadata\_meta.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:48,798 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\_functional.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:48,799 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\_common.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:48,800 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:48,801 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:48,803 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:48,804 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:48,805 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\_functional.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:48,807 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:48,808 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib\resources\readers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:48,809 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:48,811 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:48,817 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\_common.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:48,820 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:48,821 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\future\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:48,823 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:48,826 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\inspect.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:48,828 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:48,829 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ipaddress.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:48,830 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\json\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:48,833 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\json\decoder.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:48,835 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\readers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:48,836 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\json\scanner.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:48,837 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\importlib_resources\future\adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:48,839 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\logging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:48,840 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\json\encoder.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:48,841 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\jaraco.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:48,841 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\mimetypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:48,843 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\lzma.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:48,856 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:48,857 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\context.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:48,858 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:48,859 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\pool.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:48,861 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\dummy\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:48,863 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\forkserver.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:48,864 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\dummy\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:48,867 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\heap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:48,868 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\popen_forkserver.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:48,869 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\popen_fork.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:48,870 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\managers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:48,875 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\popen_spawn_posix.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:48,878 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\queues.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:48,880 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\reduction.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:48,882 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\popen_spawn_win32.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:48,884 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\resource_sharer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:48,887 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\netrc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:48,888 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\numbers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:48,888 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\process.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:48,890 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\sharedctypes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:48,891 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\resource_tracker.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:48,892 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\spawn.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:48,893 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\shared_memory.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:48,894 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:48,895 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\nturl2path.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:48,896 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\opcode.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:48,897 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\multiprocessing\synchronize.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:48,901 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\_elffile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:48,902 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\_musllinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:48,903 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\_manylinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:48,905 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:48,906 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:48,908 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\licenses\_spdx.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:48,912 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\licenses\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:48,914 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\_structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:48,916 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\specifiers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:48,917 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\markers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:48,920 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\_tokenizer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:48,921 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\requirements.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:48,923 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:48,924 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:48,926 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pathlib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:48,928 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pathlib\_abc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:48,932 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pprint.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:48,933 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\py_compile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:48,935 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\platform.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:48,936 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pickle.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:48,936 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\packaging\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:48,941 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pathlib\_local.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:48,943 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\quopri.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:48,944 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pydoc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:48,946 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pydoc_data\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:48,953 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pydoc_data\topics.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:48,954 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\queue.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:48,955 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\pkgutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:48,957 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\__version__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:48,958 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:48,959 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\_internal_utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:48,962 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\random.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:48,963 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:48,965 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\api.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:48,967 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\auth.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:48,968 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\compat.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:48,969 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\certs.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:48,970 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\cookies.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:48,974 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\models.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:48,975 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\hooks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:48,977 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:48,980 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:48,982 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\sessions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:48,983 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:48,985 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\rlcompleter.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:48,986 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\secrets.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:48,987 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\runpy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:48,991 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\status_codes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:48,991 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\requests\packages.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:48,994 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\selectors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:48,995 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_core_metadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:48,996 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:49,000 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\_modified.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:49,002 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_discovery.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:49,004 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\archive_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:49,004 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:49,006 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\cmd.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:49,007 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\command\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:49,009 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\command\build.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:49,011 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\command\bdist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:49,013 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\_log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:49,015 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\command\build_ext.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:49,017 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\ccompiler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:49,019 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\command\sdist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:49,021 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\_msvccompiler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:49,026 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:49,028 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:49,030 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compilers\C\msvc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:49,031 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compat\numpy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:49,033 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\core.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:49,034 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compilers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:49,035 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\dir_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:49,038 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compilers\C\base.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:49,040 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compilers\C\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:49,041 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\debug.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:49,042 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:49,043 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\extension.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:49,045 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\compilers\C.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:49,047 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\dist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:49,047 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\filelist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:49,048 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\file_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:49,049 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\fancy_getopt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:49,052 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\spawn.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:49,052 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\sysconfig.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:49,054 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\text_file.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:49,055 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:49,056 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:49,059 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:49,062 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_imp.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:49,063 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_normalization.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:49,064 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_distutils\versionpredicate.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:49,066 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_entry_points.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:49,069 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_importlib.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:49,071 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_shutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:49,074 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_path.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:49,077 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\backports\tarfile\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:49,081 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_static.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:49,083 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:49,084 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\backports\tarfile\compat\py38.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:49,085 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_reqs.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:49,086 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\backports\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:49,088 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:49,091 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_adapters.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:49,093 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_itertools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:49,097 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_meta.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:49,098 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_functools.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:49,099 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\backports\tarfile\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:49,100 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:49,102 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_compat.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:49,106 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:49,109 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_text.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:49,109 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:49,111 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:49,111 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\_collections.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:49,112 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\jaraco.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:49,114 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\more_itertools\recipes.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:49,116 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\jaraco\functools\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:49,117 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\jaraco\text\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:49,118 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\_elffile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:49,119 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\more_itertools\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:49,120 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\importlib_metadata\compat\py311.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:49,121 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\jaraco\context.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:49,125 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:49,126 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\_musllinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:49,131 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\more_itertools\more.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:49,137 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\_manylinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:49,137 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\specifiers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:49,139 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\_tokenizer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:49,140 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\_structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:49,142 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\tomli\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:49,143 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\markers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:49,146 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:49,149 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:49,152 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:49,153 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\packaging\requirements.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:49,155 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\tomli\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:49,157 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\tomli\_re.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:49,158 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\tomli\_types.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:49,160 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:49,164 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\typing_extensions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:49,169 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\macosx_libfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:49,170 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:49,172 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\metadata.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:49,175 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\pack.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:49,176 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\convert.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:49,181 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\unpack.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:49,181 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_elffile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:49,183 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\cli\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:49,184 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_musllinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:49,186 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:49,187 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:49,190 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_manylinux.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:49,191 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:49,193 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_structures.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:49,197 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:49,200 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\_tokenizer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:49,202 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\markers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:49,203 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\specifiers.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:49,204 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\requirements.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:49,206 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:49,208 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\tags.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:49,212 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\wheelfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:49,215 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\archive_util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:49,216 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\wheel\vendored\packaging\utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:49,218 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\zipp\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:49,218 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\zipp\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:49,219 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\_requirestxt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:49,220 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\zipp\compat\py310.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:49,220 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\_vendor\zipp\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:49,222 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\bdist_wheel.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:49,227 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:49,229 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\egg_info.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:49,234 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\bdist_egg.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:49,235 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:49,238 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\compat\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:49,239 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\setopt.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:49,240 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\sdist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:49,245 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\compat\py311.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:49,247 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_apply_pyprojecttoml.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:49,249 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_validate_pyproject\error_reporting.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:49,250 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\compat\py310.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:49,251 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\command\build.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:49,252 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_validate_pyproject\fastjsonschema_validations.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:49,253 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\compat\py39.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:49,253 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_validate_pyproject\formats.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:49,255 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_validate_pyproject\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:49,256 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_validate_pyproject\extra_validations.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:49,257 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\expand.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:49,258 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\pyprojecttoml.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:49,266 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\_validate_pyproject\fastjsonschema_exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:49,268 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\discovery.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:49,270 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\errors.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:49,271 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\config\setupcfg.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:49,272 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\depends.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:49,273 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\dist.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:49,275 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:49,279 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\installer.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:49,282 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\logging.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:49,283 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\monkey.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:49,283 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\msvc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:49,284 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\wheel.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:49,288 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:49,289 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\unicode_utils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:49,290 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\extension.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:49,291 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\warnings.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:49,293 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\shlex.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:49,296 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\shutil.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:49,299 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\site.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:49,299 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\socket.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:49,302 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\ssl.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:49,302 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\setuptools\windows_support.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:49,304 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\socketserver.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:49,305 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\signal.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:49,308 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\stringprep.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:49,309 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\statistics.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:49,311 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\sysconfig\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:49,312 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\string.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:49,314 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tarfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:49,314 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tempfile.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:49,316 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\textwrap.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:49,318 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\subprocess.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:49,320 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\threading.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:49,323 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tkinter\scrolledtext.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:49,325 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tkinter\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:49,326 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tkinter\ttk.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:49,327 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tkinter\commondialog.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:49,330 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\token.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:49,332 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tokenize.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:49,333 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tomllib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:49,335 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tkinter\constants.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:49,337 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tkinter\messagebox.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:49,341 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tty.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:49,344 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\typing.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:49,347 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tomllib\_re.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:49,348 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:49,351 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tomllib\_parser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:49,352 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tomllib\_types.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:49,353 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\tracemalloc.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:49,355 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\case.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:49,356 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\loader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:49,358 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\async_case.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:49,358 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\_log.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:49,360 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\main.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:49,362 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\result.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:49,363 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\mock.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_43  
2025-08-01 21:28:49,364 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_11  
2025-08-01 21:28:49,365 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\runner.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_40  
2025-08-01 21:28:49,370 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\suite.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_22  
2025-08-01 21:28:49,372 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib\request.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_56  
2025-08-01 21:28:49,373 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib\error.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_50  
2025-08-01 21:28:49,374 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib\parse.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_27  
2025-08-01 21:28:49,376 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\unittest\signals.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_36  
2025-08-01 21:28:49,379 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_20  
2025-08-01 21:28:49,380 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_21  
2025-08-01 21:28:49,382 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_12  
2025-08-01 21:28:49,385 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\_base_connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_15  
2025-08-01 21:28:49,389 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\_request_methods.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_34  
2025-08-01 21:28:49,391 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\connectionpool.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_45  
2025-08-01 21:28:49,392 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\_collections.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_46  
2025-08-01 21:28:49,394 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_3  
2025-08-01 21:28:49,395 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_60  
2025-08-01 21:28:49,397 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\_version.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_39  
2025-08-01 21:28:49,400 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\emscripten\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_9  
2025-08-01 21:28:49,402 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\emscripten\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_0  
2025-08-01 21:28:49,405 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\socks.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_28  
2025-08-01 21:28:49,406 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\emscripten\fetch.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_59  
2025-08-01 21:28:49,409 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\pyopenssl.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_16  
2025-08-01 21:28:49,415 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\emscripten\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_26  
2025-08-01 21:28:49,415 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\contrib\emscripten\request.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_25  
2025-08-01 21:28:49,417 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\filepost.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_29  
2025-08-01 21:28:49,419 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\http2\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_52  
2025-08-01 21:28:49,419 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_4  
2025-08-01 21:28:49,422 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\fields.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_23  
2025-08-01 21:28:49,426 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\poolmanager.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_32  
2025-08-01 21:28:49,428 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\http2\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_37  
2025-08-01 21:28:49,430 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\http2\probe.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_49  
2025-08-01 21:28:49,431 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\ssl_match_hostname.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_13  
2025-08-01 21:28:49,432 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_41  
2025-08-01 21:28:49,433 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_58  
2025-08-01 21:28:49,435 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\connection.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_42  
2025-08-01 21:28:49,436 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\proxy.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_30  
2025-08-01 21:28:49,441 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\request.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_18  
2025-08-01 21:28:49,443 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\retry.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_24  
2025-08-01 21:28:49,444 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\ssl_.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_14  
2025-08-01 21:28:49,445 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\uuid.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_62  
2025-08-01 21:28:49,448 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\version_checker.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_1  
2025-08-01 21:28:49,450 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\url.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_2  
2025-08-01 21:28:49,450 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\webbrowser.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_8  
2025-08-01 21:28:49,451 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_55  
2025-08-01 21:28:49,452 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\ssltransport.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_7  
2025-08-01 21:28:49,452 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\parsers\expat.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_17  
2025-08-01 21:28:49,454 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\timeout.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_57  
2025-08-01 21:28:49,455 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\response.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_5  
2025-08-01 21:28:49,456 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\util.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_10  
2025-08-01 21:28:49,457 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\parsers\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_35  
2025-08-01 21:28:49,459 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\sax\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_47  
2025-08-01 21:28:49,459 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\urllib3\util\wait.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_53  
2025-08-01 21:28:49,463 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\sax\_exceptions.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_63  
2025-08-01 21:28:49,465 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\sax\expatreader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_19  
2025-08-01 21:28:49,466 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\sax\handler.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_31  
2025-08-01 21:28:49,467 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\sax\saxutils.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_61  
2025-08-01 21:28:49,468 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xml\sax\xmlreader.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_48  
2025-08-01 21:28:49,470 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xmlrpc\client.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_38  
2025-08-01 21:28:49,471 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\zipfile\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_44  
2025-08-01 21:28:49,472 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\zipfile\_path\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_6  
2025-08-01 21:28:49,473 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\xmlrpc\__init__.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_33  
2025-08-01 21:28:49,474 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\zipimport.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_51  
2025-08-01 21:28:49,475 [!] Exception on decompile bytecode file:decompiled_source\PYZ.pyz_extract\zipfile\_path\glob.pyc,with error:[!] invalid pyc-struct data :__init__.py:45-30  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.decompile_pyc) at 14824@ThreadPoolExecutor-0_54  
2025-08-01 21:28:50,110 [*] 
progress_check completed :__init__.py:102-10  D:\soft\miniconda\Lib\site-packages\pydumpck\py_package\package_struct\__init__.py on(__init__.progress_check) at 14824@MainThread  
2025-08-01 21:28:50,112 [+] completed,cost 10375ms with result:94 arch file(s) handled. :__init__.py:177-20  D:\soft\miniconda\Lib\site-packages\pydumpck\py_common_dump\__init__.py on(__init__.statistics_status) at 14824@MainThread  
